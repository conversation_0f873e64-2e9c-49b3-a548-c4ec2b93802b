import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { SchoolDTO } from './school.dto';
import { ModelSchemas } from '@/schema/model.schema';
import schoolController from './school.controller';

export default async function schoolRoutes(app: FastifyInstance) {
  app.get('/school', {
    handler: schoolController.getSchools,
    schema: {
      summary: 'Get schools',
      description: 'Get schools',
      tags: ['school'],
      security: [],
      querystring: SchoolDTO.GetSchoolsSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.SchoolSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });
  app.get('/school/:id', {
    handler: schoolController.getSchoolById,
    schema: {
      summary: 'Get school by id',
      description: 'Get school by id',
      tags: ['school'],
      security: [],
      params: SchoolDTO.GetSchoolSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
  app.post('/school', {
    handler: schoolController.createSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create school',
      description: 'Create school',
      consumes: ['multipart/form-data'],
      tags: ['school', 'admin'],
      body: SchoolDTO.CreateSchoolSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });
  app.patch('/school/:id', {
    handler: schoolController.updateSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update school information',
      description: 'Update school information',
      consumes: ['multipart/form-data'],
      tags: ['school', 'admin'],
      params: SchoolDTO.UpdateSchoolParamsSchema,
      body: SchoolDTO.UpdateSchoolBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.SchoolSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
  app.delete('/school/:id', {
    handler: schoolController.deleteSchool,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete school',
      description: 'Delete school',
      tags: ['school', 'admin'],
      params: SchoolDTO.DeleteSchoolSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
