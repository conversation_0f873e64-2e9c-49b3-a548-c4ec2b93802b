import usersController from './users.controller';

import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { UsersDTO } from './users.dto';
import { Role } from '@prisma/client';
import { ModelSchemas } from '@/schema/model.schema';

export default async function usersRoutes(app: FastifyInstance) {
  app.get('/me', {
    handler: usersController.getMe,
    preHandler: [app.authenticate],
    schema: {
      summary: 'Get current user',
      description: 'Returns profile information of the authenticated user.',
      tags: ['users'],
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/users', {
    handler: usersController.getUsers,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get list of users',
      description:
        'Retrieve a paginated list of users. Optional filters like role, email, etc. can be applied.',
      tags: ['users', 'admin'],
      querystring: UsersDTO.GetUsersSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.UserSchema), true),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/users/:id', {
    handler: usersController.getUserById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Get user by ID',
      description:
        'Returns detail of a user by ID. Returns 404 if the user does not exist or was soft-deleted.',
      tags: ['users', 'admin'],
      params: UsersDTO.GetUserByIdSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/users', {
    handler: usersController.createUser,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create a new user',
      description: 'Creates a new user with required fields.',
      tags: ['users', 'admin'],
      body: UsersDTO.CreateUserSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.UserSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });

  // app.put('/users', {
  //   schema: {
  //     summary: 'Update user information',
  //     description: 'Update fields for a user. Partial update supported.',
  //     response: {
  //       200: createSuccessSchema(ModelSchemas.UserSchema),
  //       400: createErrorSchema(1001),
  //       401: createErrorSchema(401),
  //       404: createErrorSchema(404),
  //       500: createErrorSchema(500),
  //     },
  //   },
  // });

  app.delete('/users/:id', {
    handler: usersController.deleteUserById,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Soft delete a user',
      description:
        'Marks a user as deleted by setting deletedAt timestamp. The user will be excluded from future queries.',
      tags: ['users', 'admin'],
      params: UsersDTO.DeleteUserByIdSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
