import { TSchema, Type } from '@sinclair/typebox';

export const PasswordSchema = Type.String({
  minLength: 8,
  maxLength: 64,
  pattern: '^(?=.*[A-Z])(?=.*\\d)[A-Za-z\\d@$!%*?&]+$',
  description:
    'Password must be at least 8 characters long and contain at least one uppercase letter and one number',
});

export const Nullable = <T extends TSchema>(schema: T) =>
  Type.Optional(Type.Union([schema, Type.Null()]));

export const PaginationSchema = Type.Object({
  page: Type.Optional(Type.Integer({ minimum: 1 })),
  limit: Type.Optional(Type.Integer({ minimum: 1, maximum: 100 })),
});

export const SearchSchema = Type.Object({
  search: Type.Optional(Type.String({ minLength: 1 })),
});

export const SortSchema = Type.Object({
  sort: Type.Optional(Type.String({ minLength: 1 })),
  order: Type.Optional(Type.String({ enum: ['asc', 'desc'] })),
});
