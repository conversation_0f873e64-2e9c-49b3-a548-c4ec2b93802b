import { FastifyRequest, FastifyReply } from 'fastify';
import { NewsSchemaTypes } from './news.schema';

import newsService from './news.service';

class NewsController {
  async createArticle(request: FastifyRequest, reply: FastifyReply) {
    const { title, content } = request.body as NewsSchemaTypes['CreateArticle'];
    const article = await newsService.create(title, content);
    reply.sendJson(article);
  }

  async getArticleBySlug(request: FastifyRequest, reply: FastifyReply) {
    const { slug } = request.params as NewsSchemaTypes['GetArticleBySlug'];
    const article = await newsService.getBySlug(slug);
    if (!article) throw request.server.errors.NOT_FOUND;
    reply.sendJson(article);
  }

  async getArticles(request: FastifyRequest, reply: FastifyReply) {
    const { limit, page } = request.query as NewsSchemaTypes['GetArticles'];
    const articles = await newsService.getList(limit, page);
    reply.sendJson(articles);
  }
}

export default new NewsController();
