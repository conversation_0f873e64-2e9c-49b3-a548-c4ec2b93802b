import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Nullable } from './global.schema';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';

const RoleSchema = Type.String({ enum: [...Object.values(Role)] });

const CompanySchema = Type.Object({
  name: Type.String(),
  address: Type.String(),
  description: Type.String(),
  website: Type.String(),
  logoUrl: Type.String(),
});

const TiktokVideoSchema = Type.Object({
  id: Type.Number(),
  author: Nullable(Type.String()),
  caption: Nullable(Type.String()),
  thumbnail: Nullable(Type.String()),
  videoUrl: Type.String(),
});

const QuizSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  description: Nullable(Type.String()),
  kahootUrl: Type.String(),
  imageUrl: Nullable(Type.String()),
  tags: Type.Array(Type.String()),
});

const SchoolSchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  city: Nullable(Type.String()),
  address: Nullable(Type.String()),
  description: Nullable(Type.String()),
  logoUrl: Nullable(Type.String()),
  website: Nullable(Type.String()),
  latitude: Nullable(Type.Number()),
  longitude: Nullable(Type.Number()),
});

const ProfileSchema = Type.Object({
  avatar: Nullable(Type.String()),
  bio: Nullable(Type.String()),
  address: Nullable(Type.String()),
  phone: Nullable(Type.String()),
  dob: Nullable(Type.String()),
  resumeUrl: Nullable(Type.String()),
  completedAt: Nullable(Type.String({ format: 'date-time' })),
});

const EmployerSchema = Type.Object({});

const UserSchema = Type.Object({
  id: Type.Number(),
  email: Type.String(),
  name: Type.String(),
  role: RoleSchema,
  profile: ProfileSchema,
  employer: Nullable(Type.String()),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
});

export const ModelSchemas = {
  RoleSchema,
  UserSchema,
  ProfileSchema,
  EmployerSchema,
  TiktokVideoSchema,
  QuizSchema,
  SchoolSchema,
  CompanySchema,
};

export type ModelSchemaTypes = StaticFromSchema<typeof ModelSchemas>;
