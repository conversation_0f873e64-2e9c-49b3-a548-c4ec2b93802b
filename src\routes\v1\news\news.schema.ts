import { PaginationSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';

const ArticleSchema = Type.Object({
  id: Type.Number(),
  title: Type.String(),
  content: Type.String(),
  slug: Type.String(),
  image: Type.String(),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
});

const CreateArticleSchema = Type.Object({
  title: Type.String(),
  content: Type.String(),
  slug: Type.String(),
  image: Type.Optional(Type.String()),
});

const UpdateArticleSchema = Type.Object({
  title: Type.String(),
  content: Type.String(),
  image: Type.Optional(Type.String()),
});

const GetArticleBySlugSchema = Type.Object({
  slug: Type.String(),
});

const GetArticlesSchema = PaginationSchema;

export const NewsSchemas = {
  // Model
  ArticleSchema,

  // DTO
  CreateArticleSchema,
  UpdateArticleSchema,
  GetArticleBySlugSchema,
  GetArticlesSchema,
};

export type NewsSchemaTypes = StaticFromSchema<typeof NewsSchemas>;
