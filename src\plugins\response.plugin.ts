import fp from 'fastify-plugin';
import { findErrorByCode } from './errors.plugin';
import { TSchema, Type } from '@sinclair/typebox';
import { Prisma } from '@prisma/client';
import { translate } from './i18n.plugin';
import { ApiError } from '@/common/classes/ApiError';

export const createSuccessSchema = (
  dataSchema: TSchema | undefined = undefined,
  hasPagination = false,
) => {
  return Type.Object({
    success: Type.Boolean({ example: true }),
    statusCode: Type.Number({ example: 200 }),
    ...(dataSchema && { data: dataSchema }),
    ...(hasPagination && {
      pagination: Type.Object({
        total: Type.Number({ example: 100 }),
        page: Type.Number({ example: 1 }),
        limit: Type.Number({ example: 10 }),
      }),
    }),
  });
};

export const createErrorSchema = (code: number = 1000) => {
  const error = findErrorByCode(code);

  return Type.Object({
    success: Type.Boolean({ example: false }),
    statusCode: Type.Number({ example: error.statusCode }),
    error: Type.Object({
      message: Type.String({ example: translate(error.message) }),
      customCode: Type.Number({ example: error.customCode }),
      errorKey: Type.String({ example: error.code }),
      errors: Type.Optional(Type.Any({ example: 'Debug errors' })),
    }),
  });
};

const responsePlugin = fp(async (app) => {
  app.decorateReply('sendJson', function (this, data, pagination) {
    const response: any = {
      success: true,
      statusCode: this.statusCode ?? 200,
      data,
    };

    if (pagination) {
      response.pagination = pagination;
    }

    this.send(response);
  });

  app.setErrorHandler((error, request, reply) => {
    const statusCode = error.statusCode || 500;
    const isDevelopment = app.config.NODE_ENV === 'development';

    app.log.error(error);

    // Catch Prisma errors
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
        error = app.errors.NOT_FOUND;
      }
    }

    // Catch all errors
    if (!(error instanceof ApiError)) {
      error = app.errors.UNKNOWN_ERROR;
      error.statusCode = statusCode;
    }

    const apiError = error as ApiError;

    reply.status(statusCode).send({
      success: false,
      statusCode,
      error: {
        message: request.i18n.t(apiError.message) || apiError.message,
        customCode: apiError.customCode || statusCode,
        errorKey: apiError.code,
        ...(isDevelopment && { errors: apiError.errors }),
      },
    });
  });

  app.setNotFoundHandler((request, reply) => {
    const error = app.errors.NOT_FOUND;
    reply.status(error.statusCode).send({
      success: false,
      statusCode: error.statusCode,
      error: {
        message: request.i18n.t(error.message),
        customCode: error.customCode || error.statusCode,
        errorKey: error.code,
      },
    });
  });

  app.setSchemaErrorFormatter((errors, dataVar) => {
    const validationError = app.errors.VALIDATION_ERROR;
    const message = `${dataVar} ${errors[0].message}`;
    console.log(errors);
    // validationError.message = message;
    validationError.errors = errors;
    return app.errors.VALIDATION_ERROR;
  });
});

export default responsePlugin;
