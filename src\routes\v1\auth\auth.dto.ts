import { PasswordSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';

const AuthResponseSchema = Type.Object({
  accessToken: Type.String(),
  //  refreshToken: Type.String(),
});

const LoginSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
});

const RegisterSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
  name: Type.String({ minLength: 1 }),
});

export const AuthDTO = { AuthResponseSchema, LoginSchema, RegisterSchema };
export type AuthDTOTypes = StaticFromSchema<typeof AuthDTO>;
