import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class SchoolService {
  async create(options: {
    name: string;
    city?: string;
    address?: string;
    description?: string;
    website?: string;
    latitude?: number;
    longitude?: number;
    logoKey?: string;
  }) {
    const { name, city, address, description, website, latitude, longitude, logoKey } = options;

    return await withTransaction(async (tx) => {
      return await tx.school.create({
        data: {
          name,
          city,
          address,
          description,
          website,
          latitude,
          longitude,
          logoKey,
        },
      });
    });
  }

  async updateById(
    id: number,
    options: {
      name?: string;
      city?: string;
      address?: string;
      description?: string;
      website?: string;
      latitude?: number;
      longitude?: number;
      logoKey?: string;
    },
  ) {
    const { name, city, address, description, website, latitude, longitude, logoKey } = options;

    return await withTransaction(async (tx) => {
      return await tx.school.update({
        where: { id },
        data: {
          name,
          city,
          address,
          description,
          website,
          latitude,
          longitude,
          logoKey,
        },
      });
    });
  }

  async getById(id: number) {
    return await prisma.school.findUnique({ where: { id } });
  }

  async getList(options: { limit?: number; page?: number; search?: string; city?: string }) {
    const { limit = 10, page = 1, search, city } = options;

    const where: Prisma.SchoolWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
        { address: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (city) {
      if (where.OR) {
        where.AND = [{ city: { contains: city, mode: 'insensitive' } }];
      } else {
        where.city = { contains: city, mode: 'insensitive' };
      }
    }

    const [schools, count] = await prisma.$transaction([
      prisma.school.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.school.count({ where }),
    ]);

    return { schools, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.school.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new SchoolService();
