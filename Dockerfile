FROM node:20-alpine AS builder
WORKDIR /app
COPY package.json yarn.lock ./
COPY prisma ./
RUN yarn install --frozen-lockfile
COPY . .
RUN yarn build

FROM node:20-alpine
WORKDIR /app
COPY --from=builder /app/package.json ./
COPY --from=builder /app/yarn.lock ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/public ./public

# ENV NODE_ENV=production
CMD ["node", "dist/index.js"]
