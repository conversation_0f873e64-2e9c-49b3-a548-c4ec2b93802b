import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

function normalizeTag(tag: string) {
  return tag.trim().toLowerCase();
}

function normalizeTags(tags: string[]) {
  return tags.map((tag) => normalizeTag(tag));
}

class QuizService {
  async create(options: {
    title: string;
    description?: string;
    kahootUrl: string;
    tags?: string[];
    imageKey?: string;
  }) {
    const { title, description, kahootUrl, tags = [], imageKey } = options;
    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.quiz.create({
        include: { tags: true },
        data: {
          title,
          description,
          kahootUrl,
          imageKey,
          tags: {
            connectOrCreate: normalizedTags.map((name) => ({
              where: { name },
              create: { name },
            })),
          },
        },
      });
    });
  }

  async updateById(
    id: number,
    options: {
      title?: string;
      description?: string;
      kahootUrl?: string;
      tags?: string[];
      imageKey?: string;
    },
  ) {
    const { title, description, kahootUrl, tags = [], imageKey } = options;
    const normalizedTags = normalizeTags(tags);

    return await withTransaction(async (tx) => {
      return await tx.quiz.update({
        where: { id },
        include: { tags: true },
        data: {
          title,
          description,
          kahootUrl,
          imageKey,
          tags: {
            set: [],
            connectOrCreate: normalizedTags.map((name) => ({
              where: { name },
              create: { name },
            })),
          },
        },
      });
    });
  }

  async getById(id: number) {
    return await prisma.quiz.findUnique({ where: { id }, include: { tags: true } });
  }

  async getList(options: { limit?: number; page?: number; search?: string; tag?: string }) {
    const { limit = 10, page = 1, search, tag } = options;

    const where: Prisma.QuizWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { tags: { some: { name: { contains: search, mode: 'insensitive' } } } },
      ];
    }

    if (tag) {
      const normalizedTag = normalizeTag(tag);

      if (where.OR) {
        where.AND = [{ tags: { some: { name: { equals: normalizedTag } } } }];
      } else {
        where.tags = { some: { name: { equals: normalizedTag } } };
      }
    }

    const [quizs, count] = await prisma.$transaction([
      prisma.quiz.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include: { tags: true },
      }),
      prisma.quiz.count({ where }),
    ]);

    return { quizs, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.quiz.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new QuizService();
