import { FastifyReply, FastifyRequest } from 'fastify';
import { UsersDTOTypes } from './users.dto';
import { Role } from '@prisma/client';
import usersService from './users.service';

class UsersController {
  async getMe(request: FastifyRequest, reply: FastifyReply) {
    reply.sendJson(request.person);
  }

  async getUserById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as UsersDTOTypes['GetUserById'];
    const user = await usersService.getById(id);
    if (!user) throw request.server.errors.NOT_FOUND;
    reply.sendJson(user);
  }

  async getUsers(request: FastifyRequest, reply: FastifyReply) {
    const { page, limit, role, search } = request.query as UsersDTOTypes['GetUsers'];
    const result = await usersService.getList({ page, limit, role, search });
    reply.sendJson(result.persons, result.pagination);
  }

  async createUser(request: FastifyRequest, reply: FastifyReply) {
    const { email, name, password, role } = request.body as UsersDTOTypes['CreateUser'];
    const user = await usersService.create({ email, name, password, role: role as Role });
    reply.sendJson(user);
  }

  async deleteUserById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as UsersDTOTypes['DeleteUserById'];
    await usersService.deleteById(id);
    reply.sendJson();
  }
}

export default new UsersController();
