import { FastifyRequest, FastifyReply } from 'fastify';
import { SchoolDTOTypes } from './school.dto';
import { fileTypeFromBuffer } from 'file-type';
import { IMAGE_MIME_TYPES } from '@/common/constants/mime';
import { School } from '@prisma/client';
import { toFileUrl } from '@/common/utils/toFileUrl';

import schoolService from './school.service';

class SchoolController {
  async serializeSchool(school: School) {
    return {
      ...school,
      logoUrl: toFileUrl(school.logoKey),
    };
  }

  async uploadLogo(request: FastifyRequest, logo: any) {
    const fileType = await fileTypeFromBuffer(logo);
    if (!fileType || !IMAGE_MIME_TYPES.includes(fileType.mime))
      throw request.server.errors.FILE_TYPE_ERROR;

    const key = await request.server.uploadToMinio({
      buffer: logo,
      contentType: fileType.mime,
      folder: 'images',
      uploadType: 'public',
    });

    return key;
  }

  async createSchool(request: FastifyRequest, reply: FastifyReply) {
    const { name, city, address, description, website, latitude, longitude, logo } =
      request.body as SchoolDTOTypes['CreateSchool'];
    const logoKey = logo ? await this.uploadLogo(request, logo) : undefined;
    const school = await schoolService.create({
      name,
      city,
      address,
      description,
      website,
      latitude,
      longitude,
      logoKey,
    });
    reply.sendJson(this.serializeSchool(school));
  }

  async getSchoolById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['GetSchool'];
    const school = await schoolService.getById(id);
    if (!school) throw request.server.errors.NOT_FOUND;
    reply.sendJson(this.serializeSchool(school));
  }

  async getSchools(request: FastifyRequest, reply: FastifyReply) {
    const { limit = 10, page = 1, search, city } = request.query as SchoolDTOTypes['GetSchools'];
    const { schools, pagination } = await schoolService.getList({ limit, page, search, city });
    reply.sendJson(
      schools.map((school) => this.serializeSchool(school)),
      pagination,
    );
  }

  async updateSchool(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['UpdateSchoolParams'];
    const { name, city, address, description, website, latitude, longitude, logo } =
      request.body as SchoolDTOTypes['UpdateSchoolBody'];

    const logoKey = logo ? await this.uploadLogo(request, logo) : undefined;
    const school = await schoolService.updateById(id, {
      name,
      city,
      address,
      description,
      website,
      latitude,
      longitude,
      logoKey,
    });

    reply.sendJson(this.serializeSchool(school));
  }

  async deleteSchool(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as SchoolDTOTypes['DeleteSchool'];
    await schoolService.deleteById(id);
    reply.sendJson();
  }
}

export default new SchoolController();
