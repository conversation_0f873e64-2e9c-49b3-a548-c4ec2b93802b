import { PaginationSchema, PasswordSchema, SearchSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';
import { ModelSchemas } from '@/schema/model.schema';

const GetUsersSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  role: Type.Optional(ModelSchemas.RoleSchema),
});

const GetUserByIdSchema = Type.Object({
  id: Type.Number(),
});

const CreateUserSchema = Type.Object({
  email: Type.String({ format: 'email', minLength: 1 }),
  password: PasswordSchema,
  name: Type.String({ minLength: 1 }),
  role: ModelSchemas.RoleSchema,
});

const DeleteUserByIdSchema = Type.Object({
  id: Type.Number(),
});

export const UsersDTO = {
  GetUsersSchema,
  GetUserByIdSchema,
  CreateUserSchema,
  DeleteUserByIdSchema,
};

export type UsersDTOTypes = StaticFromSchema<typeof UsersDTO>;
