import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { Role } from '@prisma/client';
import { NewsSchemas } from './news.schema';

import newsController from './news.controller';

export default async function newsRoutes(app: FastifyInstance) {
  app.get('/news', {
    handler: newsController.getArticles,
    schema: {
      summary: 'Get list news',
      description: 'Get list news',
      tags: ['News'],
      querystring: NewsSchemas.GetArticlesSchema,
      response: {
        200: createSuccessSchema(Type.Array(NewsSchemas.ArticleSchema)),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });

  app.get('/news/:slug', {
    handler: newsController.getArticleBySlug,
    schema: {
      summary: 'Get news by slug',
      description: 'Get news by slug',
      tags: ['News'],
      params: NewsSchemas.GetArticleBySlugSchema,
      response: {
        200: createSuccessSchema(NewsSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });

  app.post('/news', {
    handler: newsController.createArticle,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create news',
      description: 'Create news',
      tags: ['News', 'Admin'],
      body: NewsSchemas.CreateArticleSchema,
      response: {
        200: createSuccessSchema(NewsSchemas.ArticleSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });
}
