import { prisma, withTransaction } from '@/plugins/prisma.plugin';
import { Prisma } from '@prisma/client';

class TiktokService {
  async create(options: {
    videoUrl: string;
    author?: string;
    caption?: string;
    thumbnail?: string;
  }) {
    return await withTransaction(async (tx) => {
      return await tx.tiktokVideo.create({
        data: { ...options },
      });
    });
  }

  async updateById(
    id: number,
    options: {
      videoUrl?: string;
      author?: string;
      caption?: string;
      thumbnail?: string;
    },
  ) {
    return await withTransaction(async (tx) => {
      return await tx.tiktokVideo.update({
        where: { id },
        data: options,
      });
    });
  }

  async getById(id: number) {
    return await prisma.tiktokVideo.findUnique({ where: { id } });
  }

  async getList(options: { limit?: number; page?: number; search?: string }) {
    const { limit = 10, page = 1, search } = options;
    const where: Prisma.TiktokVideoWhereInput = {};

    if (search) {
      where.OR = [
        { author: { contains: search, mode: 'insensitive' } },
        { caption: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [tiktokVideos, count] = await prisma.$transaction([
      prisma.tiktokVideo.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
      }),
      prisma.tiktokVideo.count({ where }),
    ]);

    return { tiktokVideos, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.tiktokVideo.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new TiktokService();
