import { FastifyInstance } from 'fastify';

import authRoutes from './auth/auth.route';
import newsRoutes from './news/news.route';
import tiktokRoutes from './tiktok/tiktok.route';
import usersRoutes from './users/users.route';
import quizRoutes from './quiz/quiz.route';
import schoolRoutes from './school/school.route';

export default async function v1Routes(app: FastifyInstance) {
  await app.register(authRoutes);
  await app.register(usersRoutes);
  await app.register(tiktokRoutes);
  await app.register(quizRoutes);
  await app.register(schoolRoutes);
  // await app.register(newsRoutes);
}
