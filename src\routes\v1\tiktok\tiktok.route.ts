import { FastifyInstance } from 'fastify';
import { createErrorSchema, createSuccessSchema } from '@/plugins/response.plugin';
import { Type } from '@sinclair/typebox';
import { TiktokDTO } from './tiktok.dto';
import { ModelSchemas } from '@/schema/model.schema';
import { Role } from '@prisma/client';
import tiktokController from './tiktok.controller';

export default async function tiktokRoutes(app: FastifyInstance) {
  app.get('/tiktok', {
    handler: tiktokController.getTiktokVideos,
    schema: {
      summary: 'Get Tiktok videos',
      description: 'Get Tiktok videos',
      tags: ['tiktok'],
      security: [],
      querystring: TiktokDTO.GetTiktokVideosSchema,
      response: {
        200: createSuccessSchema(Type.Array(ModelSchemas.TiktokVideoSchema), true),
        400: createErrorSchema(1001),
        500: createErrorSchema(500),
      },
    },
  });
  app.get('/tiktok/:id', {
    handler: tiktokController.getTiktokVideoById,
    schema: {
      summary: 'Get Tiktok video by id',
      description: 'Get Tiktok video by id',
      tags: ['tiktok'],
      security: [],
      params: TiktokDTO.GetTiktokVideoSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TiktokVideoSchema),
        400: createErrorSchema(),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
  app.post('/tiktok', {
    handler: tiktokController.createTiktokVideo,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Create Tiktok video',
      description: 'Create Tiktok video',
      tags: ['tiktok', 'admin'],
      body: TiktokDTO.CreateTikTokVideoSchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TiktokVideoSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        500: createErrorSchema(500),
      },
    },
  });
  app.patch('/tiktok/:id', {
    handler: tiktokController.updateTiktokVideo,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Update Tiktok video',
      description: 'Update Tiktok video',
      tags: ['tiktok', 'admin'],
      params: TiktokDTO.UpdateTiktokVideoParamsSchema,
      body: TiktokDTO.UpdateTiktokVideoBodySchema,
      response: {
        200: createSuccessSchema(ModelSchemas.TiktokVideoSchema),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
  app.delete('/tiktok/:id', {
    handler: tiktokController.deleteTiktokVideo,
    preHandler: [app.authenticate, app.roleAuthenticate([Role.ADMIN])],
    schema: {
      summary: 'Delete Tiktok video',
      description: 'Delete Tiktok video',
      tags: ['tiktok', 'admin'],
      params: TiktokDTO.DeleteTiktokVideoSchema,
      response: {
        200: createSuccessSchema(),
        400: createErrorSchema(1001),
        401: createErrorSchema(401),
        404: createErrorSchema(404),
        500: createErrorSchema(500),
      },
    },
  });
}
