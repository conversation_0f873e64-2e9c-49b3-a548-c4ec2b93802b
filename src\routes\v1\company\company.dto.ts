import { PaginationSchema } from '@/schema/global.schema';
import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { Type } from '@sinclair/typebox';
import { ModelSchemas } from '@/schema/model.schema';

const GetUsersSchema = Type.Object({
  ...PaginationSchema.properties,
  role: Type.Optional(ModelSchemas.RoleSchema),
  email: Type.Optional(Type.String({ format: 'email' })),
  name: Type.Optional(Type.String()),
});

const GetUserByIdSchema = Type.Object({
  id: Type.Number(),
});

const CreateUserSchema = Type.Object({
  email: Type.String({ format: 'email' }),
  password: Type.String(),
  name: Type.String(),
  role: ModelSchemas.RoleSchema,
});

const DeleteUserByIdSchema = Type.Object({
  id: Type.Number(),
});

export const UsersDTO = {
  GetUsersSchema,
  GetUserByIdSchema,
  CreateUserSchema,
  DeleteUserByIdSchema,
};

export type UsersDTOTypes = StaticFromSchema<typeof UsersDTO>;
