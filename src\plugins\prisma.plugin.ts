import fp from 'fastify-plugin';
import { Prisma, PrismaClient } from '@prisma/client';

export const prisma = new PrismaClient();

prisma.$use(async (params, next) => {
  if (['findMany', 'findFirst'].includes(params.action)) {
    if (!params.args) params.args = {};
    if (!params.args.where) params.args.where = {};
    params.args.where.deletedAt = null;
  }

  return next(params);
});

export const withTransaction = async <T>(
  fn: (tx: Prisma.TransactionClient) => Promise<T>,
): Promise<T> => {
  return await prisma.$transaction(fn);
};

const prismaPlugin = fp(async (app) => {
  try {
    // Connect to the database
    await prisma.$connect();
  } catch (error) {
    console.error(error);
    process.exit(1);
  }

  app.decorate('prisma', prisma);
  app.decorate('withTransaction', withTransaction);
  app.addHook('onClose', async (instance) => {
    await prisma.$disconnect();
  });
});

export default prismaPlugin;
