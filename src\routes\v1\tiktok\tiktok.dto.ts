import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateTikTokVideoSchema = Type.Object({
  videoUrl: Type.String({ format: 'uri', minLength: 1 }),
  author: Type.Optional(Type.String({ minLength: 1 })),
  caption: Type.Optional(Type.String({ minLength: 1 })),
  thumbnail: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
});

const UpdateTiktokVideoParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateTiktokVideoBodySchema = Type.Object({
  videoUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  author: Type.Optional(Type.String({ minLength: 1 })),
  caption: Type.Optional(Type.String({ minLength: 1 })),
  thumbnail: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
});

const GetTiktokVideoSchema = Type.Object({
  id: Type.Number(),
});

const GetTiktokVideosSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
});

const DeleteTiktokVideoSchema = Type.Object({
  id: Type.Number(),
});

export const TiktokDTO = {
  CreateTikTokVideoSchema,
  UpdateTiktokVideoParamsSchema,
  UpdateTiktokVideoBodySchema,
  GetTiktokVideoSchema,
  GetTiktokVideosSchema,
  DeleteTiktokVideoSchema,
};

export type TiktokDTOTypes = StaticFromSchema<typeof TiktokDTO>;
