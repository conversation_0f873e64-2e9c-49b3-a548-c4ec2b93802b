import { FastifyRequest, FastifyReply } from 'fastify';
import { AccountType, Provider } from '@prisma/client';
import { AuthDTOTypes } from './auth.dto';

import usersService from '../users/users.service';

class AuthController {
  async register(request: FastifyRequest, reply: FastifyReply) {
    const { email, password, name } = request.body as AuthDTOTypes['Register'];

    const _person = await usersService.getByEmail(email);
    if (_person) throw request.server.errors.EMAIL_ALREADY_EXISTS;

    const person = await usersService.create({ email, password, name });

    const payload = { id: person.id, email: person.email };
    const accessTokenExpirationTime = request.server.config.JWT_ACCESS_EXPIRATION_TIME || '7d';
    const accessToken = request.server.jwt.sign(payload, { expiresIn: accessTokenExpirationTime });

    reply.sendJson({ accessToken });
  }

  async login(request: FastifyRequest, reply: FastifyReply) {
    const { email, password } = request.body as AuthDTOTypes['Login'];

    const person = await usersService.getByEmail(email);
    if (!person) throw request.server.errors.INVALID_CREDENTIALS;

    const validPassword = await usersService.comparePassword(person, password);
    if (!validPassword) throw request.server.errors.INVALID_CREDENTIALS;

    const payload = { id: person.id, email: person.email };
    const accessTokenExpirationTime = request.server.config.JWT_ACCESS_EXPIRATION_TIME || '7d';
    const accessToken = request.server.jwt.sign(payload, { expiresIn: accessTokenExpirationTime });

    reply.sendJson({ accessToken });
  }

  async gooogleCallback(request: FastifyRequest, reply: FastifyReply) {
    const { token } =
      await request.server.googleOAuth2.getAccessTokenFromAuthorizationCodeFlow(request);

    const googleUser: any = await request.server.googleOAuth2.userinfo(token);
    let person = await usersService.getByEmail(googleUser.email);

    if (!person) {
      person = await request.server.prisma.person.create({
        data: {
          email: googleUser.email,
          name: googleUser.name,
          password: '',
          profile: {
            create: {
              avatar: googleUser.picture,
            },
          },
        },
      });
    }

    await request.server.prisma.account.upsert({
      where: {
        provider_providerAccountId: {
          provider: Provider.GOOGLE,
          providerAccountId: googleUser.id,
        },
      },
      update: {
        accessToken: token.access_token,
        refreshToken: token.refresh_token,
        expiresAt: token.expires_in ? Math.floor(Date.now() / 1000) + token.expires_in : undefined,
        tokenType: token.token_type,
        idToken: token.id_token,
      },
      create: {
        personId: person.id,
        type: AccountType.OAUTH,
        provider: Provider.GOOGLE,
        providerAccountId: googleUser.id,
        accessToken: token.access_token,
        refreshToken: token.refresh_token,
        expiresAt: token.expires_in ? Math.floor(Date.now() / 1000) + token.expires_in : undefined,
        tokenType: token.token_type,
        idToken: token.id_token,
      },
    });

    const payload = { id: person.id, email: person.email };
    const accessTokenExpirationTime = request.server.config.JWT_ACCESS_EXPIRATION_TIME || '7d';
    const accessToken = request.server.jwt.sign(payload, { expiresIn: accessTokenExpirationTime });

    reply.sendJson({ accessToken: accessToken });
  }
}

export default new AuthController();
