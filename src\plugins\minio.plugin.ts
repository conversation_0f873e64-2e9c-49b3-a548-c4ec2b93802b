import fp from 'fastify-plugin';
import { Client } from 'minio';
import { v4 as uuidv4 } from 'uuid';

const minioPlugin = fp(async (app) => {
  const publicBucket = app.config.MINIO_BUCKET;
  const privateBucket = `${app.config.MINIO_BUCKET}-private`;

  const minioClient = new Client({
    endPoint: app.config.MINIO_ENDPOINT,
    port: app.config.MINIO_PORT,
    useSSL: false,
    accessKey: app.config.MINIO_ACCESS_KEY,
    secretKey: app.config.MINIO_SECRET_KEY,
  });

  const exists = await minioClient.bucketExists(publicBucket);
  if (!exists) await minioClient.makeBucket(publicBucket);

  const privateExists = await minioClient.bucketExists(privateBucket);
  if (!privateExists) await minioClient.makeBucket(privateBucket);

  app.decorate('minio', minioClient);
  app.decorate('uploadToMinio', async (file) => {
    const { buffer, contentType, folder, uploadType = 'public' } = file;

    const bucket = uploadType === 'public' ? publicBucket : privateBucket;
    const objectName = folder ? `${folder}/${uuidv4()}` : uuidv4();

    await minioClient.putObject(bucket, objectName, buffer, buffer.length, {
      'Content-Type': contentType,
    });
    return objectName;
  });

  app.get('/file/:key', async (req, reply) => {
    const { key } = req.params as { key: string };

    try {
      const stream = await minioClient.getObject('my-bucket', key);
      reply.header('Content-Type', 'image/jpeg').send(stream);
    } catch {
      throw app.errors.NOT_FOUND;
    }
  });
});

export default minioPlugin;
