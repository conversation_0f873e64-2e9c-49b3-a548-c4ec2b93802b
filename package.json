{"name": "france-api", "version": "1.0.0", "main": "index.js", "license": "MIT", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "postinstall": "prisma generate", "prisma": "prisma", "prisma:migrate": "prisma migrate", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:seed": "node prisma/seed.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "format": "prettier --write ."}, "dependencies": {"@fastify/ajv-compiler": "^4.0.2", "@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.3", "@fastify/oauth2": "^8.1.2", "@fastify/static": "^8.2.0", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@fastify/type-provider-typebox": "^5.1.0", "@prisma/client": "^6.10.1", "@sinclair/typebox": "^0.34.37", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "fastify": "^5.4.0", "file-type": "^21.0.0", "minio": "^8.0.5", "pino-pretty": "^13.0.0", "slugify": "^1.6.6", "url-join": "^5.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "prettier": "^3.6.2", "prisma": "^6.10.1", "tsc-alias": "^1.8.16", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1"}}