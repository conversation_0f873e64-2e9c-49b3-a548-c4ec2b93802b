# Environment settings
NODE_ENV=development

# Api settings
API_NAME=france-api
API_PORT=3000
API_HOST=0.0.0.0
API_VERSION=1.0.0
API_PREFIX=/api
API_URL=http://localhost:3000

# OAuth settings
GOOGLE_CLIENT_ID=client_id
GOOGLE_CLIENT_SECRET=client_secret

# Database settings
# DATABASE_URL=mysql://root:123456@localhost:3306/france-db
DATABASE_URL=postgres://postgres:123456@localhost:5432/france-db

# JWT settings
JWT_ACCESS_SECRET=secret
JWT_ACCESS_EXPIRATION_TIME=1d
JWT_REFRESH_SECRET=secret
JWT_REFRESH_EXPIRATION_TIME=7d
