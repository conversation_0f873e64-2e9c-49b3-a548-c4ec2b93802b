import { prisma } from '@/plugins/prisma.plugin';
import { Person, Prisma, Role } from '@prisma/client';

import bcrypt from 'bcryptjs';

class UsersService {
  async create(options: { email: string; name: string; password: string; role?: Role }) {
    const { email, name, password, role = Role.USER } = options;
    const hashedPassword = await bcrypt.hash(password, 10);

    return await prisma.person.create({
      include: { profile: true },
      data: {
        email,
        name,
        password: hashedPassword,
        role,
        profile: {
          create: {},
        },
      },
    });
  }

  async comparePassword(person: Person, password: string) {
    if (!person.password || person.password === '') return false;
    return await bcrypt.compare(password, person.password);
  }

  async getByEmail(email: string) {
    return await prisma.person.findUnique({ where: { email } });
  }

  async getById(id: number) {
    return await prisma.person.findUnique({
      where: { id },
      include: { profile: true },
      omit: { password: true },
    });
  }

  async getList(options: {
    limit?: number;
    page?: number;
    role?: string;
    email?: string;
    name?: string;
  }) {
    const { limit = 10, page = 1, role, email, name } = options;
    const where: Prisma.PersonWhereInput = {};

    if (role) where.role = role as Role;
    if (email) where.email = { contains: email, mode: 'insensitive' };
    if (name) where.name = { contains: name, mode: 'insensitive' };

    const [persons, count] = await prisma.$transaction([
      prisma.person.findMany({
        take: limit,
        skip: (page - 1) * limit,
        where,
        include: {
          profile: true,
          employer: true,
        },
      }),
      prisma.person.count({ where }),
    ]);

    return { persons, pagination: { total: count, page, limit } };
  }

  async deleteById(id: number) {
    return await prisma.person.update({
      where: { id },
      data: { deletedAt: new Date() },
    });
  }
}

export default new UsersService();
