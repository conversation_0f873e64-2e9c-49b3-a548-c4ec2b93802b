// Simple test script to verify School API endpoints
const BASE_URL = 'http://127.0.0.1:3000';

async function testSchoolAPI() {
  console.log('Testing School API...\n');

  try {
    // Test GET /school (should work without authentication)
    console.log('1. Testing GET /school');
    const response = await fetch(`${BASE_URL}/school`);
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('✅ GET /school works\n');

    // Test GET /school/:id with a non-existent ID
    console.log('2. Testing GET /school/999 (non-existent)');
    const response2 = await fetch(`${BASE_URL}/school/999`);
    const data2 = await response2.json();
    console.log('Status:', response2.status);
    console.log('Response:', JSON.stringify(data2, null, 2));
    console.log('✅ GET /school/:id handles non-existent ID correctly\n');

    console.log('🎉 All basic School API tests passed!');
    console.log('\nNote: POST, PATCH, DELETE endpoints require admin authentication.');
    console.log('You can test them through the Swagger UI at http://127.0.0.1:3000/docs');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSchoolAPI();
