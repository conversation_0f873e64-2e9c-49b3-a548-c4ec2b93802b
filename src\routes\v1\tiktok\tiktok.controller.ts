import { FastifyRequest, FastifyReply } from 'fastify';
import { TiktokDTOTypes } from './tiktok.dto';
import tiktokService from './tiktok.service';

class TiktokController {
  async createTiktokVideo(request: FastifyRequest, reply: FastifyReply) {
    const { videoUrl, author, caption, thumbnail } =
      request.body as TiktokDTOTypes['CreateTikTokVideo'];
    const tiktokVideo = await tiktokService.create({ videoUrl, author, caption, thumbnail });
    reply.sendJson(tiktokVideo);
  }

  async getTiktokVideoById(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TiktokDTOTypes['GetTiktokVideo'];
    const tiktokVideo = await tiktokService.getById(id);
    if (!tiktokVideo) throw request.server.errors.NOT_FOUND;
    reply.sendJson(tiktokVideo);
  }

  async getTiktokVideos(request: FastifyRequest, reply: FastifyReply) {
    const { limit = 10, page = 1, search } = request.query as TiktokDTOTypes['GetTiktokVideos'];
    const { tiktokVideos, pagination } = await tiktokService.getList({ limit, page, search });
    reply.sendJson(tiktokVideos, pagination);
  }

  async updateTiktokVideo(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TiktokDTOTypes['UpdateTiktokVideoParams'];
    const { videoUrl, author, caption, thumbnail } =
      request.body as TiktokDTOTypes['UpdateTiktokVideoBody'];
    const tiktokVideo = await tiktokService.updateById(id, {
      videoUrl,
      author,
      caption,
      thumbnail,
    });
    reply.sendJson(tiktokVideo);
  }

  async deleteTiktokVideo(request: FastifyRequest, reply: FastifyReply) {
    const { id } = request.params as TiktokDTOTypes['DeleteTiktokVideo'];
    await tiktokService.deleteById(id);
    reply.sendJson();
  }
}

export default new TiktokController();
