generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
  EMPLOYER
}

model Person {
  id            Int              @id @default(autoincrement())
  name          String
  email         String           @unique
  emailVerified DateTime?
  password      String
  role          Role             @default(USER)
  accounts      Account[]
  profile       Profile?
  employer      Employer?
  applications  JobApplication[]
  notifications Notification[]
  files         File[]
  // 
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  deletedAt     DateTime?
}

enum AccountType {
  OAUTH
  CREDENTIALS
}

enum Provider {
  GOOGLE
}

model Account {
  personId          Int
  person            Person      @relation(fields: [personId], references: [id])
  type              AccountType
  provider          Provider
  providerAccountId String
  refreshToken      String?
  accessToken       String?
  expiresAt         Int?
  tokenType         String?
  scope             String?
  idToken           String?
  //
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  deletedAt         DateTime?

  @@id([provider, providerAccountId])
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

model Profile {
  personId    Int       @unique
  person      Person    @relation(fields: [personId], references: [id])
  avatar      String?
  bio         String?   @db.Text
  address     String?   @db.Text
  phone       String?
  dob         DateTime?
  gender      Gender?
  resumeKey   String?
  completedAt DateTime?
  //
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

model Employer {
  person    Person    @relation(fields: [personId], references: [id])
  personId  Int       @unique
  company   Company   @relation(fields: [companyId], references: [id])
  companyId Int
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Company {
  id               Int        @id @default(autoincrement())
  name             String
  address          String     @db.Text
  description      String?    @db.Text
  website          String?
  logoKey          String?
  jobs             Job[]
  employerProfiles Employer[]
  // 
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt
  deletedAt        DateTime?
}

model JobTag {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  jobs      Job[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Job {
  id           Int              @id @default(autoincrement())
  title        String           @db.Text
  description  String           @db.Text
  requirements String?          @db.Text
  salary       String?          @db.Text
  company      Company          @relation(fields: [companyId], references: [id])
  companyId    Int
  applications JobApplication[]
  tags         JobTag[]
  //
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  deletedAt    DateTime?
}

model JobApplication {
  id          Int       @id @default(autoincrement())
  person      Person    @relation(fields: [personId], references: [id])
  personId    Int
  job         Job       @relation(fields: [jobId], references: [id])
  jobId       Int
  coverLetter String?   @db.Text
  resumeKey   String?
  appliedAt   DateTime  @default(now())
  //
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

model Notification {
  id        Int       @id @default(autoincrement())
  person    Person    @relation(fields: [personId], references: [id])
  personId  Int
  message   String    @db.Text
  read      Boolean   @default(false)
  url       String?
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Article {
  id            Int       @id @default(autoincrement())
  title         String    @db.Text
  content       String    @db.Text
  summary       String?   @db.Text
  slug          String    @unique
  featuredImage String?
  //
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  deletedAt     DateTime?
}

model TiktokVideo {
  id        Int       @id @default(autoincrement())
  author    String?
  caption   String?
  thumbnail String?
  videoUrl  String
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model School {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  city        String?
  address     String?   @db.Text
  description String?   @db.Text
  logoKey     String?
  website     String?
  latitude    Float?
  longitude   Float?
  //
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

model QuizTag {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  quizzes   Quiz[]
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}

model Quiz {
  id          Int       @id @default(autoincrement())
  title       String    @db.Text
  description String?   @db.Text
  kahootUrl   String?
  imageKey    String?
  tags        QuizTag[]
  //
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
}

model File {
  id        Int       @id @default(autoincrement())
  key       String    @unique
  owner     Person?   @relation(fields: [ownerId], references: [id])
  ownerId   Int?
  mimeType  String?
  //
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
}
