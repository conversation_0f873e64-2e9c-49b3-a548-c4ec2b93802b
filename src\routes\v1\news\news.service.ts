import { prisma, withTransaction } from '@/plugins/prisma.plugin';

import slugify from 'slugify';

class NewsService {
  async create(title: string, content: string) {
    const slug = slugify(title);
    return await withTransaction(async (tx) => {
      return await tx.article.create({
        data: { title, slug, content },
      });
    });
  }

  async getBySlug(slug: string) {
    const article = await prisma.article.findUnique({ where: { slug } });
    return article;
  }

  async getList(limit = 10, page = 1) {
    return await prisma.article.findMany({
      take: limit,
      skip: (page - 1) * limit,
    });
  }
}

export default new NewsService();
