import { StaticFromSchema } from '@/common/utils/StaticFromSchema';
import { PaginationSchema, SearchSchema } from '@/schema/global.schema';
import { Type } from '@sinclair/typebox';

const CreateQuizSchema = Type.Object({
  title: Type.String({ minLength: 1 }),
  description: Type.Optional(Type.String()),
  kahootUrl: Type.String({ format: 'uri', minLength: 1 }),
  tags: Type.Optional(Type.Array(Type.String())),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const UpdateQuizParamsSchema = Type.Object({
  id: Type.Number(),
});

const UpdateQuizBodySchema = Type.Object({
  title: Type.Optional(Type.String({ minLength: 1 })),
  description: Type.Optional(Type.String()),
  kahootUrl: Type.Optional(Type.String({ format: 'uri', minLength: 1 })),
  tags: Type.Optional(Type.Array(Type.String())),
  image: Type.Optional(Type.Any({ isFile: true })),
});

const GetQuizSchema = Type.Object({
  id: Type.Number(),
});

const GetQuizsSchema = Type.Object({
  ...PaginationSchema.properties,
  ...SearchSchema.properties,
  tag: Type.Optional(Type.String()),
});

const DeleteQuizSchema = Type.Object({
  id: Type.Number(),
});

export const QuizDTO = {
  CreateQuizSchema,
  UpdateQuizParamsSchema,
  UpdateQuizBodySchema,
  GetQuizSchema,
  GetQuizsSchema,
  DeleteQuizSchema,
};

export type QuizDTOTypes = StaticFromSchema<typeof QuizDTO>;
